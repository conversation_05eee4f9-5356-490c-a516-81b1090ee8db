import * as bootstrap from 'bootstrap'

// Type declaration za window.bootstrap
declare global {
  interface Window {
    bootstrap: any
  }
}

export default defineNuxtPlugin(() => {
  // Inicijalizuj Bootstrap komponente kada se DOM učita
  if (process.client) {
    console.log('Bootstrap plugin se učitava...')

    const initBootstrap = () => {
      console.log('Inicijalizujem Bootstrap komponente...')
      
      // Inicijalizuj dropdown komponente
      const dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]')
      console.log(`Pronašao ${dropdownElementList.length} dropdown elemenata`)

      dropdownElementList.forEach(dropdownToggleEl => {
        // Uništi postojeću instancu ako postoji
        const existingInstance = bootstrap.Dropdown.getInstance(dropdownToggleEl)
        if (existingInstance) {
          existingInstance.dispose()
        }
        
        // Kreiraj novu instancu
        new bootstrap.Dropdown(dropdownToggleEl, {
          autoClose: true,
          boundary: 'clippingParents'
        })
      })

      // Inicijalizuj collapse komponente
      const collapseElementList = document.querySelectorAll('[data-bs-toggle="collapse"]')
      console.log(`Pronašao ${collapseElementList.length} collapse elemenata`)

      collapseElementList.forEach(collapseToggleEl => {
        const existingInstance = bootstrap.Collapse.getInstance(collapseToggleEl)
        if (existingInstance) {
          existingInstance.dispose()
        }
        
        new bootstrap.Collapse(collapseToggleEl, { toggle: false })
      })

      // Inicijalizuj modal komponente
      const modalElementList = document.querySelectorAll('.modal')
      console.log(`Pronašao ${modalElementList.length} modal elemenata`)

      modalElementList.forEach(modalEl => {
        const existingInstance = bootstrap.Modal.getInstance(modalEl)
        if (existingInstance) {
          existingInstance.dispose()
        }
        
        new bootstrap.Modal(modalEl)
      })
    }

    // Inicijalizuj kada se DOM učita
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(initBootstrap, 100)
      })
    } else {
      setTimeout(initBootstrap, 100)
    }

    // Reinicijalizuj nakon navigacije
    const router = useRouter()
    router.afterEach(() => {
      setTimeout(initBootstrap, 100)
    })
  }

  return {
    provide: {
      bootstrap
    }
  }
})
