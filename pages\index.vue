<template>
  <div>
    <!-- Navbar -->
    <AppNavbar />

    <!-- Main Content -->
    <div class="container my-5">
      <!-- Breadcrumb -->
      <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <a href="https://www.akademijaoxford.com" class="text-decoration-none">Početna</a>
          </li>
          <li class="breadcrumb-item active" aria-current="page">Cenovnik</li>
        </ol>
      </nav>

      <!-- Page Title -->
      <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary mb-3">CENOVNIK KURSEVA</h1>
        <hr class="w-25 mx-auto mb-4" style="height: 3px; background-color: #f38630;">
      </div>

      <!-- Cities Grid -->
      <div class="row g-3">
        <div v-for="grad in gradovi" :key="grad.slug" class="col-lg-3 col-md-4 col-sm-6 col-12">
          <NuxtLink
            :to="`/cenovnik/${grad.slug}`"
            class="city-link d-block p-3 text-center text-decoration-none border rounded hover-shadow"
            :title="`Cenovnik kurseva - ${grad.naziv}, cene`"
          >
            {{ grad.naziv }}
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <AppFooter />
  </div>
</template>

<script setup>
// SEO meta tags
useHead({
  title: 'Cenovnik Akademija Oxford',
  meta: [
    { name: 'description', content: 'Akademija Oxford - Cenovnik za sve kurseve i obuke po gradovima' },
    { name: 'keywords', content: 'cenovnik akademija oxford, cene kurseva, cene obuka, oxford, akademija' },
    { property: 'og:title', content: 'Cenovnik Akademija Oxford' },
    { property: 'og:description', content: 'Akademija Oxford - Cenovnik za sve kurseve i obuke po gradovima' },
    { property: 'og:url', content: 'https://www.akademijaoxford.com/cenovnik/' },
    { property: 'og:site_name', content: 'Akademija Oxford' }
  ]
})

// Bootstrap inicijalizacija
const { initializeAllComponents } = useBootstrap()

// Inicijalizuj Bootstrap komponente kada se komponenta mount-uje
onMounted(() => {
  initializeAllComponents()
})

// Lista gradova sa slug-ovima
const gradovi = [
  { naziv: 'Aranđelovac', slug: 'arandjelovac' },
  { naziv: 'Banovo Brdo', slug: 'banovo-brdo' },
  { naziv: 'Beograd', slug: 'beograd' },
  { naziv: 'Bor', slug: 'bor' },
  { naziv: 'Borča', slug: 'borca' },
  { naziv: 'Čačak', slug: 'cacak' },
  { naziv: 'Ćuprija', slug: 'cuprija' },
  { naziv: 'Inđija', slug: 'indjija' },
  { naziv: 'Jagodina', slug: 'jagodina' },
  { naziv: 'Kragujevac', slug: 'kragujevac' },
  { naziv: 'Kraljevo', slug: 'kraljevo' },
  { naziv: 'Kruševac', slug: 'krusevac' },
  { naziv: 'Leskovac', slug: 'leskovac' },
  { naziv: 'Mladenovac', slug: 'mladenovac' },
  { naziv: 'Niš', slug: 'nis' },
  { naziv: 'Novi Beograd', slug: 'novi-beograd' },
  { naziv: 'Novi Pazar', slug: 'novi-pazar' },
  { naziv: 'Novi Sad', slug: 'novi-sad' },
  { naziv: 'Obrenovac', slug: 'obrenovac' },
  { naziv: 'Pančevo', slug: 'pancevo' },
  { naziv: 'Paraćin', slug: 'paracin' },
  { naziv: 'Požarevac', slug: 'pozarevac' },
  { naziv: 'Smederevo', slug: 'smederevo' },
  { naziv: 'Sombor', slug: 'sombor' },
  { naziv: 'Subotica', slug: 'subotica' },
  { naziv: 'Sr. Mitrovica', slug: 'sremska-mitrovica' },
  { naziv: 'Šabac', slug: 'sabac' },
  { naziv: 'Užice', slug: 'uzice' },
  { naziv: 'Valjevo', slug: 'valjevo' },
  { naziv: 'Voždovac', slug: 'vozdovac' },
  { naziv: 'Vranje', slug: 'vranje' },
  { naziv: 'Vršac', slug: 'vrsac' },
  { naziv: 'Zaječar', slug: 'zajecar' },
  { naziv: 'Zemun', slug: 'zemun' },
  { naziv: 'Zrenjanin', slug: 'zrenjanin' },
  { naziv: 'Ruma', slug: 'ruma' }
]
</script>

<style scoped>
.city-link {
  background: #fff;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  border-color: #dee2e6 !important;
}

.city-link:hover {
  background: #f38630;
  color: white !important;
  transform: translateY(-2px);
  border-color: #f38630 !important;
}

.hover-shadow:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.text-primary {
  color: #f38630 !important;
}

.navbar-brand img {
  height: 40px;
}

/* Mega menu styles */
.mega-menu {
  position: static !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  margin-top: 0 !important;
}

.mega-menu .container-fluid {
  max-width: 1200px;
  margin: 0 auto;
}

.mega-menu-content {
  padding: 2rem 0;
}

.mega-menu-column h6 {
  color: #f38630;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f38630;
}

.mega-menu-column .dropdown-item {
  padding: 0.5rem 0;
  border: none;
  color: #333;
  transition: all 0.3s ease;
}

.mega-menu-column .dropdown-item:hover {
  background: transparent;
  color: #f38630;
  padding-left: 0.5rem;
}

/* Regular dropdown positioning */
.dropdown-menu:not(.mega-menu) {
  min-width: 250px;
}

@media (max-width: 991px) {
  .mega-menu {
    position: relative !important;
    width: auto !important;
    box-shadow: none !important;
  }

  .mega-menu-content {
    padding: 1rem 0;
  }

  .mega-menu-column {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 576px) {
  .city-link {
    padding: 0.75rem !important;
    font-size: 0.9rem;
  }
}
</style>

