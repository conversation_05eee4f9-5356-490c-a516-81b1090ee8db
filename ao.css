@import url("https://fonts.googleapis.com/css2?family=Fjalla+One&display=swap");

@font-face {
  font-family: "Roboto";
  src: url("https://www.akademijaoxford.com/fonts/Roboto-Regular-webfont.eot");
  src: url("https://www.akademijaoxford.com/fonts/Roboto-Regular-webfont.eot?#iefix")
      format("embedded-opentype"),
    url("https://www.akademijaoxford.com/fonts/Roboto-Regular-webfont.woff")
      format("woff"),
    url("https://www.akademijaoxford.com/fonts/Roboto-Regular-webfont.ttf")
      format("truetype"),
    url("https://www.akademijaoxford.com/fonts/Roboto-Regular-webfont.svg#robotoregular")
      format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Roboto Condensed";
  src: url("https://www.akademijaoxford.com/fonts/RobotoCondensed-Regular-webfont.eot");
  src: url("https://www.akademijaoxford.com/fonts/RobotoCondensed-Regular-webfont.eot?#iefix")
      format("embedded-opentype"),
    url("https://www.akademijaoxford.com/fonts/RobotoCondensed-Regular-webfont.woff")
      format("woff"),
    url("https://www.akademijaoxford.com/fonts/RobotoCondensed-Regular-webfont.ttf")
      format("truetype"),
    url("https://www.akademijaoxford.com/fonts/RobotoCondensed-Regular-webfont.svg#roboto_condensedregular")
      format("svg");
  font-weight: normal;
  font-style: normal;
}

body {
  margin: 0;
  background-color: #fff;
  padding-top: 65px;
}

.navbar-form input,
.form-inline input {
  width: auto;
}

header {
  height: auto;
}

#nav.affix {
  position: fixed;
  top: 0;
  max-width: 980px;
  width: 100%;
  z-index: 100;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.topnav {
  height: 30px;
  background-color: #080808;
  margin-top: -10px;
}

.topnav ul {
  line-height: 30px;
  margin-left: -30px;
}

.topnav ul li {
  color: #fff;
  float: left;
  font-size: 12px;
  padding-right: 15px;
  list-style-type: none;
  font-family: "Roboto", sans-serif;
}

.content {
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 50px;
  width: auto;
  height: auto;
}

p {
  text-align: justify;
  font-family: "Roboto", sans-serif;
}

.centrirano {
  text-align: left;
  /*font-size: 13px;*/
}

.centrirano a {
  /*color: #060606;*/
  text-decoration: none;
  border-bottom: 2px solid #eee;
}

.centrirano a:hover {
  color: #060606;
  border-bottom: 2px solid #060606;
}

.predavaci {
  text-align: left;
  font-family: "Roboto", sans-serif;
}

.row.kontakt {
  padding-left: 15px;
  padding-right: 15px;
}

.desno {
  max-width: 270px;
  width: auto;
  height: auto;
  margin-left: auto;
  margin-right: auto;
  margin-top: 19px;
}

.bar {
  max-width: 270px;
  width: auto;
  height: auto;
  background-color: #080808;
  background-image: url("../images/zig-zag.png");
  font-family: "Roboto", sans-serif;
  font-size: 14px;
  color: #fff;
  line-height: 40px;
  text-align: center;
  vertical-align: middle;
}

.bar-b {
  max-width: 270px;
  width: auto;
  height: auto;
  font-family: "Roboto Condensed", sans-serif;
  font-size: 18px;
  font-weight: bold;
  color: #080808;
  text-align: left;
  border-bottom: 3px solid #ff7319;
  margin-bottom: 20px;
}

.bar-c {
  max-width: 270px;
  width: auto;
  height: auto;
  font-family: "Roboto Condensed", sans-serif;
  font-size: 18px;
  font-weight: bold;
  color: #080808;
  text-align: left;
  border-bottom: 3px solid #ff7319;
  margin-bottom: 20px;
  margin-top: 20px;
}

#marqueecontainer {
  position: relative;
  width: auto;
  height: 250px;
  background-color: #fff;
  overflow: hidden;
  border: 1px solid #ccc;
  left: 0;
  margin-left: auto;
  margin-right: auto;
  font-family: "Roboto", sans-serif;
  padding: 10px;
}

#marqueecontainer h5 {
  font-family: "Roboto", sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #ff7319;
}

#marqueecontainer p {
  font-size: 13px;
  color: #080808;
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}

#vmarquee {
  max-width: 250px;
  width: auto;
}

.col-sm-4 img {
  margin-left: auto;
  margin-right: auto;
}

.logocontainer {
  background-color: #f0f0f0;
  vertical-align: middle;
  padding-top: 25px;
  padding-bottom: 10px;
  background-image: url("../header.png");
  background-repeat: no-repeat;
  background-position: right;
}

.logocontainer h6 {
  margin-top: 15px;
  margin-left: 30px;
}

#footer {
  background-color: #080808;
  background-image: url("../images/zig-zag.png");
  height: auto;
  border-top: 6px;
  border-bottom: 6px;
  border-top-color: #f27223;
  border-bottom-color: #f27223;
  border-top-style: solid;
  border-bottom-style: solid;
  font-family: "Roboto", sans-serif;
  margin-top: 20px;
}

#footer .col-md-3 {
  text-align: center;
  color: #fff;
  padding-top: 10px;
  padding-bottom: 0;
}

#footer .col-sm-6 {
  text-align: center;
  color: #fff;
  padding-top: 10px;
  padding-bottom: 0;
}

#footer .col-xs-12 {
  text-align: center;
  color: #fff;
  padding-top: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}

#footer ul {
  text-align: center;
  padding-top: 0;
  margin-left: -40px;
}

#footer ul li {
  list-style: none;
  padding-bottom: 10px;
}

#footer h4 {
  font-weight: bold;
  color: #f27223;
  margin-top: 10px;
  margin-bottom: 15px;
}

ul.pagination {
  margin: 0;
  margin-bottom: 50px;
  height: auto;
  max-height: 35px;
  overflow: visible;
  font: 12px "Roboto", sans-serif;
  list-style-type: none;
}

ul.pagination li.details {
  padding: 7px;
  font-size: 12px;
}

ul.pagination li.dot {
  padding: 3px 0;
}

ul.pagination li {
  float: left;
  margin: 5px;
}

ul.pagination li a {
  display: block;
  color: #000;
  text-decoration: none;
  padding: 7px;
}

ul.pagination li a img {
  border: none;
}

ul.pagination li.details {
  color: #888;
}

ul.pagination li a {
  color: #fff;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

ul.pagination li a {
  color: #fff;
  border: 0;
  padding: 6px 9px;
  background-color: #ff970c;
  border-radius: 0;
}

ul.pagination li a:hover,
ul.pagination li a.current {
  background-color: #ff7319;
  color: #fff;
}

.icons {
  width: 220px;
  float: right;
  height: 25px;
  margin-top: 60px;
  z-index: 9999;
  position: relative;
  margin-right: 25px;
}

.icons ul {
  margin: 0;
}

.icons ul li {
  float: right;
  padding-right: 5px;
  list-style-type: none;
  margin: 0;
}

.social-container {
  background-image: -webkit-linear-gradient(top, #f7971c 0, #f27223 100%);
  background-image: linear-gradient(to bottom, #f7971c 0, #f27223 100%);
  background-image: -webkit-gradient(
    linear,
    50% 0%,
    50% 100%,
    color-stop(0%, #f7971c),
    color-stop(100%, #f27223)
  );
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff27223', endColorstr='#fff7971c', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
  background-repeat: repeat-x;
}

.social-icons {
  margin-bottom: 0;
}

.social-icons li {
  display: inline-block;
  font-size: 30px;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 10px;
}

.social-icons li a {
  color: #080808 !important;
}

.social-icons li a:hover {
  color: #fff !important;
}

.jezik {
  width: 220px;
  float: right;
  height: 25px;
  margin-top: 5px;
  z-index: 9999;
  position: relative;
  margin-right: 25px;
}

.jezik ul {
  margin: 0;
}

.jezik ul li {
  float: left;
  padding-right: 5px;
  list-style-type: none;
  margin: 0;
  position: relative;
}

.korekcija {
  margin: 0;
  padding-bottom: 10px;
}

.video-container {
  position: relative;
  padding-bottom: 60%;
  padding-top: 0;
  height: 0;
  overflow: hidden;
}

.video-container iframe,
.video-container object,
.video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 93%;
}

.spoilerdugme {
  background-image: -webkit-linear-gradient(top, #f7971c 0, #f27223 100%);
  background-image: linear-gradient(to bottom, #f7971c 0, #f27223 100%);
  background-image: -webkit-gradient(
    linear,
    50% 0%,
    50% 100%,
    color-stop(0%, #f7971c),
    color-stop(100%, #f27223)
  );
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#fff27223', endColorstr='#fff7971c', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  background-repeat: repeat-x;
  background-color: #f9f9f9;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  border: 0;
  display: inline-block;
  cursor: pointer;
  color: #fff;
  font-family: "Roboto", sans-serif;
  font-size: 13px;
  padding: 6px 24px;
  text-decoration: none;
  text-shadow: 0 0 0 #fff;
}

.spoilerdugme:hover {
  background-image: -webkit-linear-gradient(top, #f27223 0, #f7971c 100%);
  background-image: linear-gradient(to bottom, #f27223 0, #f7971c 100%);
  background-image: -webkit-gradient(
    linear,
    50% 0%,
    50% 100%,
    color-stop(0%, #f27223),
    color-stop(100%, #f7971c)
  );
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#fff27223', endColorstr='#fff7971c', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  background-repeat: repeat-x;
  background-color: #e9e9e9;
}

.spoilerdugme:active {
  position: relative;
  top: 0;
}

.spoilerdugme {
  display: block;
  margin: 5px 0;
}

.spoiler {
  overflow: hidden;
  background: #fff;
}

.spoiler > div {
  -webkit-transition: all 0.4s ease;
  -moz-transition: margin 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: margin 0.4s ease;
}

.spoilerdugme[value="PrikaÅ¾i gradove"] + .spoiler > div {
  display: none;
}
.spoilerdugme[value="Sakrij"] + .spoiler {
  padding: 5px;
}

.rslides {
  position: relative;
  list-style-type: none;
  overflow: hidden;
  max-width: 269px;
  width: 100%;
  padding: 0;
  margin: 0;
}

.rslides ul li {
  list-style-type: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: relative;
  display: block;
  width: 100%;
  left: 0;
  top: 0;
}

li#rslides1_s1 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

li#rslides1_s2 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

li#rslides1_s3 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

li#rslides1_s4 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

li#rslides1_s5 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

li#rslides1_s6 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

li#rslides1_s7 {
  display: block;
  float: none;
  position: absolute;
  opacity: 0;
  z-index: 1;
  -webkit-transition: opacity 1500ms ease-in-out;
  transition: opacity 1500ms ease-in-out;
}

.rslides li:first-child {
  position: relative;
  display: block;
  float: left;
  list-style-type: none;
}

.rslides img {
  display: block;
  height: auto;
  float: left;
  width: 100%;
  border: 0;
}

.scrollup {
  width: 40px;
  height: 40px;
  position: fixed;
  bottom: 150px;
  right: 0;
  display: none;
  text-indent: -9999px;
  background: url("../images/icon_top.png") no-repeat;
  z-index: 9999;
}

:-moz-any-link:focus {
  outline: none;
}

.form_content {
  width: 280px;
  height: auto;
  overflow: hidden;
  padding: 25px 20px 17px;
  border: 1px dashed #d2d2d2;
  margin-top: 30px;
  float: right;
  -webkit-border-radius: 0.5em;
  -moz-border-radius: 0.5em;
  border-radius: 0.5em;
}

.prijava-form_content {
  width: auto;
  height: auto;
  overflow: hidden;
  padding: 25px 20px 17px;
  border: 1px dashed #d2d2d2;
  margin-top: 30px;
  -webkit-border-radius: 0.5em;
  -moz-border-radius: 0.5em;
  border-radius: 0.5em;
}

.form_element {
  position: absolute;
  width: 39px;
  height: 47px;
  background-image: url(../content/form_element.png);
  margin-top: -30px;
  margin-left: 230px;
}

.form_error_text {
  color: #f47a20;
  margin: 0;
  padding: 0;
}

.form_error {
  height: auto;
  min-height: 19px;
  margin-top: -3px;
  overflow: hidden;
}

.form_elements {
  margin-top: 25px;
  height: auto;
}

.form_elements_row {
  width: auto;
  height: auto;
  overflow: hidden;
  margin-bottom: 10px;
  padding-bottom: 5px;
}

.form_input {
  float: right;
  height: 30px;
  width: auto;
  border: 1px dashed #d2d2d2;
  background-color: #fff;
  padding: 0 10px;
  font: 12px Arial, Helvetica, sans-serif;
  color: #080808;
  -webkit-border-radius: 0.2em;
  -moz-border-radius: 0.2em;
  border-radius: 0.2em;
}
.prijava_form_elements_row {
  width: auto !important;
}
.form_input_prijava {
  width: 500px !important;
}

.form_input_wide {
  float: right;
  height: 100px;
  width: auto;
  border: 1px dashed #d2d2d2;
  background-color: #fff;
  font: 12px Arial, Helvetica, sans-serif;
  color: #080808;
  padding: 10px;
  -webkit-border-radius: 0.2em;
  -moz-border-radius: 0.2em;
  border-radius: 0.2em;
}

input:focus,
textarea:focus {
  border: 1px dashed #bbb !important;
}

.form_label {
  float: left;
  font: 12px/30px Arial, Helvetica, sans-serif;
}

#form_button {
  padding: 10px 12px !important;
  float: right !important;
  margin: 15px 50px 0 0;
  outline: medium none;
}

/*
Colorbox Core Style:
The following CSS is consistent between example themes and should not be altered.
*/
#colorbox,
#cboxOverlay,
#cboxWrapper {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  overflow: hidden;
}

#cboxWrapper {
  max-width: none;
}

#cboxOverlay {
  position: fixed;
  width: 100%;
  height: 100%;
}

#cboxMiddleLeft,
#cboxBottomLeft {
  clear: left;
}

#cboxContent {
  position: relative;
}

#cboxLoadedContent {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#cboxTitle {
  margin: 0;
}

#cboxLoadingOverlay,
#cboxLoadingGraphic {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#cboxPrevious,
#cboxNext,
#cboxClose,
#cboxSlideshow {
  cursor: pointer;
}

.cboxPhoto {
  float: left;
  margin: auto;
  border: 0;
  display: block;
  max-width: none;
  -ms-interpolation-mode: bicubic;
}

.cboxIframe {
  width: 100%;
  height: 100%;
  display: block;
  border: 0;
}

#colorbox,
#cboxContent,
#cboxLoadedContent {
  box-sizing: content-box;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
}

/*
User Style:
Change the following styles to modify the appearance of Colorbox.  They are
ordered & tabbed in a way that represents the nesting of the generated HTML.
*/
#cboxOverlay {
  background: #141416;
}
#colorbox {
  outline: 0;
}

#cboxContent {
  margin-top: 32px;
  overflow: visible;
  background: #000;
}

.cboxIframe {
  background: #fff;
}

#cboxError {
  padding: 50px;
  border: 1px solid #ccc;
}

#cboxLoadedContent {
  background: #000;
  padding: 1px;
}

#cboxLoadingGraphic {
  background: url(images/loading.gif) no-repeat center center;
}

#cboxLoadingOverlay {
  background: #000;
}

#cboxTitle {
  position: absolute;
  top: -22px;
  left: 0;
  color: #000;
}

#cboxCurrent {
  position: absolute;
  top: -22px;
  right: 205px;
  text-indent: -9999px;
}

/* these elements are buttons, and may need to have additional styles reset to avoid unwanted base styles */
#cboxPrevious,
#cboxNext,
#cboxSlideshow,
#cboxClose {
  border: 0;
  padding: 0;
  margin: 0;
  overflow: visible;
  text-indent: -9999px;
  width: 20px;
  height: 20px;
  position: absolute;
  top: -20px;
  background: url(images/controls.png) no-repeat 0 0;
}

/* avoid outlines on :active (mouseclick), but preserve outlines on :focus (tabbed navigating) */
#cboxPrevious:active,
#cboxNext:active,
#cboxSlideshow:active,
#cboxClose:active {
  outline: 0;
}

#cboxPrevious {
  background-position: 0;
  right: 44px;
}

#cboxPrevious:hover {
  background-position: 0 -25px;
}

#cboxNext {
  background-position: -25px 0;
  right: 22px;
}
#cboxNext:hover {
  background-position: -25px;
}

#cboxClose {
  background-position: -50px 0;
  right: 0;
}
#cboxClose:hover {
  background-position: -50px -25px;
}

.cboxSlideshow_on #cboxPrevious,
.cboxSlideshow_off #cboxPrevious {
  right: 66px;
}

.cboxSlideshow_on #cboxSlideshow {
  background-position: -75px -25px;
  right: 44px;
}
.cboxSlideshow_on #cboxSlideshow:hover {
  background-position: -100px -25px;
}

.cboxSlideshow_off #cboxSlideshow {
  background-position: -100px 0;
  right: 44px;
}

.cboxSlideshow_off #cboxSlideshow:hover {
  background-position: -75px -25px;
}

.nav-tabs {
  border-bottom: 0;
  background-color: #e0e0e1;
}

.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}

.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 41px;
  border: 0;
  border-radius: 0;
}

.nav-tabs > li > a:hover {
  border-color: #eee #eee #ddd;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #555;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}

.nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}

.nav-tabs.nav-justified > li {
  float: none;
}

.nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
  padding: 8px;
}

.nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}

.kontakt {
  margin-bottom: 10px;
}

@media (min-width: 801px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }

  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }

  .nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 4px;
  }

  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border: 0 solid #ddd;
  }
}

@media (min-width: 801px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 0 solid #ddd;
    border-radius: 4px 4px 0 0;
  }

  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}

/* PLACEMENT TEST */
.test-holder {
  width: 928px;
  padding: 20px;
  height: 335px;
  display: block;
  margin: 25px 0 10px;
  background: #f5f5f5;
  border: 1px solid #eee;
  -webkit-box-shadow: 0 0 0 rgba(255, 255, 255, 0.1),
    0 2px 1px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 0 0 rgba(255, 255, 255, 0.1), 0 2px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 0 rgba(255, 255, 255, 0.1), 0 2px 1px rgba(0, 0, 0, 0.05);
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}

.test-holder p {
  margin: 0;
  padding: 0;
}

.pdf {
  text-align: right;
}

@media (max-width: 800px) {
  .scrollup {
    bottom: 50px;
  }

  .pdf {
    text-align: left;
  }

  .raspored-container-naslov {
    background-image: none;
  }
}

.centar {
  text-align: center;
  font-size: 15px;
  padding-top: 15px;
}

.raspored {
  text-align: center;
}

#map-beograd,
#map-jagodina,
#map-zemun,
#map-pozarevac,
#map-kragujevac,
#map-kraljevo,
#map-krusevac,
#map-cacak,
#map-uzice,
#map-nis,
#map-vranje,
#map-smederevo,
#map-novisad,
#map-subotica,
#map-pancevo,
#map-paracin,
#map-cuprija,
#map-mladenovac,
#map-zrenjanin,
#map-banovobrdo,
#map-cukarica,
#map-velikaplana,
#map-novibeograd,
#map-vozdovac,
#map-palilula,
#map-novipazar,
#map-leskovac,
#map-sabac,
#map-sombor,
#map-valjevo,
#map-stari-grad {
  height: 250px;
  margin: 20px 0;
  padding: 0;
}

.center {
  float: none;
  margin: 0 auto;
}

.fa-contact {
  padding-right: 10px;
  font-size: 16px;
}

.link-contact {
  color: #080808;
}

.link-contact a:hover {
  color: #080808;
}

.informacije {
  padding-right: 10px;
  font-size: 16px;
  color: #f59833;
}

.cenovnik {
  width: 100%;
  border: 1px solid #080808;
  text-align: left;
}

.cenovnik h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 0;
}

.cenovnik img {
  border: 1px solid #080808;
}

.cenovnik-header {
  background-color: #ef6d22;
  border: 1px solid #080808;
  height: 20px;
  color: #fff;
  font-size: 16px;
}

.cenovnik-sivo {
  background-color: #f5f5f5;
}

.cenovnik-belo {
  background-color: #fff;
}

.cellpadding {
  padding-left: 5px;
  padding-right: 5px;
}

.bold {
  font-weight: bold;
}

.gray {
  background-color: #f4f4f4;
  border: 1px solid #080808;
  padding: 10px;
  margin-bottom: 15px;
}

.rent {
  width: 100%;
  background-color: #f7971c;
}

.rent > a {
  color: #fff;
  text-shadow: 0 -1px 1px #933b0b;
  margin: 1px auto;
  display: block;
  width: 100%;
  text-align: center;
  font-size: 23px;
}

.carousel-inner-small > .item > img,
.carousel-inner > .item > a > img {
  display: block;
  height: 260px;
  max-width: 400px;
}

.carousel-inner-small {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.carousel-inner-small > .item {
  display: none;
  position: relative;
  transition: left 0.6s ease-in-out 0s;
}

.carousel-inner-small > .item > img,
.carousel-inner-small > .item > a > img {
  line-height: 1;
}

.carousel-inner-small > .active,
.carousel-inner-small > .next,
.carousel-inner-small > .prev {
  display: block;
}

.carousel-inner-small > .active {
  left: 0;
}

.carousel-inner-small > .next,
.carousel-inner-small > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}

.carousel-inner-small > .next {
  left: 100%;
}

.carousel-inner-small > .prev {
  left: -100%;
}

.carousel-inner-small > .next.left,
.carousel-inner-small > .prev.right {
  left: 0;
}

.carousel-inner-small > .active.left {
  left: -100%;
}

.carousel-inner-small > .active.right {
  left: 100%;
}

.carousel-inner {
  border: none;
}

.link {
  color: blue;
}

.margin-top {
  margin-top: 30px;
}

.naslov-vesti {
  font-size: 15px;
  margin-top: 0;
  margin-bottom: 10px;
  color: #f07435;
}

.center {
  float: none;
  margin: 0 auto;
}

/* Sudski tumaci DESNO */
ul.usluge-tumaci {
  list-style-type: none !important;
  padding: 0;
  max-width: 270px;
  margin: 0 auto;
  border-left: 1px solid #a2a194;
  border-right: 1px solid #a2a194;
  border-bottom: 1px solid #a2a194;
}

ul.usluge-tumaci li {
  list-style-image: none !important;
  display: block;
  background: #f7f7f7;
  background: -moz-linear-gradient(#f7f7f7 0%, #ececec 100%);
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #f7f7f7),
    color-stop(100%, #ececec)
  );
  background: -webkit-linear-gradient(#f7f7f7 0%, #ececec 100%);
  background: linear-gradient(#f7f7f7 0%, #ececec 100%);
  padding: 10px;
  border-bottom: 1px solid #a2a194;
  height: 90px;
  font-size: 14px;
}

ul.usluge-tumaci li img {
  float: left;
  width: 70px;
  height: 70px;
  margin-right: 10px;
}

ul.usluge-tumaci li:last-child {
  border-bottom: none;
}

ul.usluge-tumaci li a {
  color: #080808;
  font-weight: 700;
  font-family: "Roboto Condensed", sans-serif;
}

ul.usluge-tumaci li a:hover {
  color: #080808;
}

/* Sudski tumaci DESNO ALTERNATIVE */
ul.usluge-tumaci-alt {
  list-style-type: none !important;
  padding: 0;
  max-width: 270px;
  margin: 0 auto;
  border: 1px solid #a2a194;
}

ul.usluge-tumaci-alt li {
  list-style-image: none !important;
  display: block;
  background: #f7f7f7;
  background: -moz-linear-gradient(#f7f7f7 0%, #ececec 100%);
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #f7f7f7),
    color-stop(100%, #ececec)
  );
  background: -webkit-linear-gradient(#f7f7f7 0%, #ececec 100%);
  background: linear-gradient(#f7f7f7 0%, #ececec 100%);
  padding: 10px;
  border-bottom: 1px solid #a2a194;
  height: 90px;
  font-size: 14px;
}

ul.usluge-tumaci-alt li img {
  float: left;
  width: 70px;
  height: 70px;
  margin-right: 10px;
}

ul.usluge-tumaci-alt li:last-child {
  border-bottom: none;
}

ul.usluge-tumaci-alt li a {
  color: #080808;
  font-weight: 700;
  font-family: "Roboto Condensed", sans-serif;
}

ul.usluge-tumaci-alt li a:hover {
  color: #080808;
}

.bar h2 {
  font-size: 16px;
  padding: 11px 0;
  margin-bottom: 0;
}

.btn-default {
  background: none;
  font-size: 14px;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active {
  background: none;
}

.form-horizontal .control-label {
  padding-bottom: 5px;
}

.form-horizontal .form-group {
  margin-left: 0;
  margin-right: 0;
}

.dropdown-select {
  border-radius: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.dropdown-select li a {
  color: #555;
}

.dropdown-select li a:hover {
  color: #fff;
}

/* megamenu */
.fugen {
  position: relative;
}

#vertical .navbar-brand {
  width: 240px;
}

#vertical .ttmenu-full.open {
  max-width: 100%;
  width: 800px !important;
  position: absolute;
}

#vertical .navbar-default,
#vertical .navbar-default .nav,
#vertical .navbar-default .collapse,
#vertical .navbar-default .dropup,
#vertical .navbar-default .dropdown {
  position: static;
  width: 240px !important;
  left: 0;
}

#vertical .navbar-collapse {
  padding: 0;
}

#vertical .navbar-default .navbar-nav li .vertical-menu {
  opacity: 1;
  position: absolute;
  left: 240px !important;
  right: 0 !important;
  top: auto;
  margin-top: -65px !important;
  max-width: 900px;
  padding: 0;
}

#vertical .navbar-default .navbar-nav li .vertical-dropdown-menu {
  opacity: 1;
  position: absolute;
  left: 240px !important;
  right: 0 !important;
  top: auto;
  margin-top: -65px !important;
  max-width: 240px;
  padding: 0;
}

#vertical .navbar-default .navbar-nav li .dropdown {
  width: 240px !important;
}

#vertical .navbar-default .navbar-nav li ul {
  width: 100% !important;
}

#vertical .dropdown-menu,
#vertical .navbar-default .ttmenu-content {
  padding: 20px;
}

#vertical .nav-pills {
  max-width: 200px;
}

#vertical .dropme::after {
  content: "\f105";
  color: #fff;
  position: absolute;
  right: 20px;
  padding-left: 5px;
  font-family: "FontAwesome";
}

.ttmenu .navbar-default {
  border: 0;
  width: 100% !important;
  border-radius: 0;
}

.ttmenu .navbar-default .dropdown-menu {
  border: 0;
  border-radius: 0;
}

.ttmenu .navbar-default .dropdown-menu,
.ttmenu .navbar-default .dropdown-menu li a {
  color: #282828;
  list-style: none;
  background-color: transparent !important;
}

.white-style .navbar-brand i {
  color: #fff !important;
}

.ttmenu .navbar-default .navbar-toggle span {
  color: #fff !important;
  background: #fff !important;
}

.ttmenu .navbar-default .navbar-toggle {
  border-color: #111;
  color: #fff !important;
  margin-top: 15px;
}

.ttmenu .navbar ul {
  padding-left: 0;
  list-style: none;
}

.ttmenu .navbar-brand {
  float: left;
  position: relative;
}

.ttmenu .navbar-collapse {
  border-bottom: 0 !important;
}

.ttmenu .navbar-default .navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top: 3px solid #ff7319;
}

.ttmenu .navbar-default .nav,
.ttmenu .navbar-default .collapse,
.ttmenu .navbar-default .dropup,
.ttmenu .navbar-default .dropdown {
  position: static;
}

.ttmenu .navbar-default .container {
  position: relative;
}

.ttmenu .navbar-default .dropdown-menu {
  left: auto;
  background-color: #fff !important;
}

.ttmenu-content .box li,
.ttmenu .navbar-default .dropdown-menu li a {
  text-decoration: none !important;
}

.ttmenu .navbar-default .dropdown-menu,
.ttmenu-content .dropdown-menu {
  padding: 0 !important;
}

.navbar-default .navbar-nav > li > a {
  padding-bottom: 20px;
  padding-top: 23px;
  font-family: "Roboto Condensed", sans-serif;
  font-size: 13px;
  height: 65px;
  font-style: normal;
  color: #fff !important;
  text-transform: uppercase;
}

.navbar-default .dropdown-menu li a {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: #282828;
  font-size: 13px;
  font-weight: 400;
  font-family: "Roboto Condensed", Arial, sans-serif;
  padding: 13px 10px 13px 15px !important;
  position: relative;
  text-decoration: none;
  text-transform: none;
}

.ttmenu .navbar-default li .widget a {
  padding: 0 !important;
}

.ttmenu-content .box li a {
  color: #000;
  font-size: 15px;
  border-bottom: none;
  border-bottom: 0 !important;
  font-weight: 400;
  font-family: "Roboto Condensed", Arial, sans-serif;
  padding: 16px 30px 12px 0 !important;
  position: relative;
  text-decoration: none;
  text-transform: none;
  left: 32px;
}

.ttmenu .dropdown-menu,
.ttmenu .navbar-default .ttmenu-content {
  padding: 24px;
}

.ttmenu .navbar-default .ttmenu-full .dropdown-menu {
  left: 0;
  right: 0;
}

.ttmenu .nav li span.label {
  color: #fff !important;
  margin-left: 5px;
  margin-top: 2px !important;
  padding: 5px 10px;
}

.ttmenu .navbar-default .navbar-nav > .active > a,
.ttmenu .navbar-default .navbar-nav > .active > a:hover,
.ttmenu .navbar-default .navbar-nav > .active > a:focus {
  color: #ffaf4b;
  background-color: transparent;
}

.ttmenu-content .widget {
  padding: 0;
  margin: 0;
}

.ttmenu-content,
.ttmenu-content .widget,
.ttmenu-content .widget .thumb {
  position: relative;
}

.ttmenu-content .widget .title h4 {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 400;
}

.ttmenu-content .box li {
  /*    border-bottom: 1px solid #ededed; */
  border-bottom: 0;
  position: relative;
  padding: 10px 10px 10px 0;
}

.ttmenu-content .box li i {
  right: 0;
  clear: both;
  top: 10px;
  display: block;
  position: absolute;
}

.dropme::after {
  content: "\f107";
  color: #fff;
  padding-left: 5px;
  font-family: "FontAwesome";
}

.dropme-left::after {
  content: "\f105";
  color: #1a1a1a;
  font-family: "FontAwesome";
  display: block;
  right: 10px;
  width: 0;
  position: absolute;
  height: 0;
  top: 13px;
  margin-top: 0;
  margin-right: 10px;
}

.navbar-default .dropdown-menu li:last-child,
.ttmenu-content .box li:last-child {
  border-bottom: 0;
}

.box p {
  color: #1a1a1a;
  font-weight: 400;
  padding-top: 10px;
  font-size: 13px;
}

.box h4 {
  color: #1a1a1a;
  font-weight: bold;
  font-size: 14px;
}

.box ul li span {
  float: none;
  margin-top: 0;
  margin-right: 15px;
}

.icon_1 {
  background-image: url("../images/menu-icons/ic_format_color_text_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_5 {
  background-image: url("../images/menu-icons/ic_content_cut_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_2 {
  background-image: url("../images/menu-icons/ic_computer_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_3 {
  background-image: url("../images/menu-icons/ic_airplanemode_active_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_4 {
  background-image: url("../images/menu-icons/ic_supervisor_account_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_6 {
  background-image: url("../images/menu-icons/ic_border_color_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_7 {
  background-image: url("../images/menu-icons/ic_event_seat_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_8 {
  background-image: url("../images/menu-icons/ic_card_membership_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_9 {
  background-image: url("../images/menu-icons/ic_public_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_10 {
  background-image: url("../images/menu-icons/ic_headset_mic_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.icon_11 {
  background-image: url("../images/menu-icons/ic_face_black_24dp_1x.png");
  width: 24px;
  height: 24px;
}

.searchbox {
  bottom: 0;
  float: right;
  line-height: 65px;
  min-width: 65px;
  overflow: hidden;
  position: relative;
  background: #222;
  right: 0;
  width: 0;
  -webkit-transition: width 0.3s;
  -moz-transition: width 0.3s;
  -ms-transition: width 0.3s;
  -o-transition: width 0.3s;
  transition: width 0.3s;
}

.searchbox-input {
  top: 0;
  right: 0;
  border: 0;
  outline: 0;
  background: #080808;
  width: 100%;
  line-height: 65px;
  height: 65px;
  margin: 0;
  padding: 1px 65px 0 20px;
  font-size: 12px;
}

.searchbox-icon,
.searchbox-submit {
  width: 65px;
  height: 65px;
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  font-size: 18px;
  right: 0;
  padding: 0;
  margin: 0;
  border: 0;
  outline: 0;
  line-height: 65px;
  text-align: center;
  cursor: pointer;
  color: #fff;
  background: #000;
}

.ttmenu input[type="search"] {
  color: #fff;
}

.searchbox-open {
  width: 100%;
}

.ttmenu .form-control {
  background-color: #f9f9f9;
  border: 1px solid #f5f5f5;
  border-radius: 0;
  box-shadow: none;
  font-size: 12px;
  height: 35px;
  margin-bottom: 10px;
}

.ttmenu textarea {
  height: 140px !important;
}

.ttmenu .entry {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}

.ttmenu .entry img {
  width: 100%;
}

.ttmenu .magnifier {
  background: rgba(0, 0, 0, 0.9) url(images/magnifier.png) no-repeat center;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  zoom: 1;
  filter: alpha(opacity=0);
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.ttmenu .entry:hover .magnifier {
  zoom: 1;
  filter: alpha(opacity=100);
  opacity: 1;
}

.ttmenu .menu-image {
  margin-bottom: 30px;
}

#myCarousel img {
  width: 100%;
}

/* MENU TABBED */
.nav-pills {
  margin-top: 6px;
}

.nav-pills > li {
  float: none;
  margin-top: 0;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-pills > li > a {
  border-radius: 0;
  cursor: pointer !important;
  border: 0 !important;
  position: relative;
  font-weight: bold !important;
  text-transform: uppercase !important;
  margin-right: 0;
}

.nav-pills > li > a::after {
  content: "\f105";
  color: #1a1a1a;
  font-family: "FontAwesome";
  display: block;
  right: 10px;
  width: 0;
  position: absolute;
  height: 0;
  top: 13px;
  margin-top: 0;
  margin-right: 10px;
}

.demo_changer .demo-icon,
.menu-color-gradient .btn-primary,
.menu-color-gradient .label-danger,
.menu-color-gradient .navbar-default .navbar-nav > .open > a,
.menu-color-gradient .navbar-default .navbar-nav > .open > a:hover,
.menu-color-gradient .navbar-default .navbar-nav > .open > a:focus,
.menu-color-gradient .navbar-default .navbar-nav > li > a:focus,
.menu-color-gradient .navbar-default .navbar-nav > li > a:active,
.menu-color-gradient .navbar-default .navbar-nav > li > a.active,
.menu-color-gradient .navbar-default .navbar-nav > li > a:hover {
  background-image: -ms-linear-gradient(top, #ff970c 0%, #ff7319 100%);
  background-image: -moz-linear-gradient(top, #ff970c 0%, #ff7319 100%);
  background-image: -o-linear-gradient(top, #ff970c 0%, #ff7319 100%);
  background-image: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0, #ff970c),
    color-stop(100, #ff7319)
  );
  background-image: -webkit-linear-gradient(top, #ff970c 0%, #ff7319 100%);
  background-image: linear-gradient(to bottom, #ff970c 0%, #ff7319 100%);
}

.menu-color-gradient .navbar-default .navbar-toggle:hover,
.menu-color-gradient .coldesc:hover {
  background: #fb7118;
  color: #fff !important;
}

.menu-color-gradient .form-control:focus {
  border-color: #a90329;
  outline: 0;
  box-shadow: none;
}

.menu-color-gradient .btn-primary {
  border-radius: 0;
  font-size: 13px;
  background-color: #a90329 !important;
  border-color: #a90329 !important;
  color: #fff !important;
}

.menu-color-gradient .box ul li:hover .fa {
  color: #a90329 !important;
}

.menu-color-gradient .magnifier {
  background-color: rgba(169, 3, 41, 0.9);
}

.dark-style .coldesc {
  background: #080808;
  color: #fff;
  margin-bottom: 10px;
  text-align: center;
}

.dark-style .navbar-default .ttmenu-content {
  border-radius: 0;
}

.dark-style .dropdown-menu {
  background-color: #fff !important;
}

.dark-style .navbar-default .dropdown-menu li a,
.dark-style .ttmenu-content .box li a,
.dark-style .ttmenu-content .box li::before {
  color: #080808 !important;
}

.dark-style .ttmenu-content .box li a:hover,
.dark-style .ttmenu-content .box li a:focus {
  background: none !important;
}

.dark-style .ttmenu-content .box li {
  border-bottom-color: rgba(0, 0, 0, 0.1);
}

.dark-style .navbar-default .dropdown-menu li a:hover,
.dark-style .navbar-default .dropdown-menu li a:focus {
  background: #fff;
}

.dark-style .form-control,
.dark-style .searchbox-icon,
.searchbox-submit {
  background: #45484d; /* Old browsers */

  /* IE9 SVG, needs conditional override of 'filter' to 'none' */
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzQ1NDg0ZCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
  background: -moz-linear-gradient(top, #45484d 0%, #000 100%); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #45484d),
    color-stop(100%, #000)
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    #45484d 0%,
    #000 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, #45484d 0%, #000 100%); /* Opera 11.10+ */
  background: -ms-linear-gradient(top, #45484d 0%, #000 100%); /* IE10+ */
  background: linear-gradient(to bottom, #45484d 0%, #000 100%); /* W3C */
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#45484d', endColorstr='#000', GradientType=0); /* IE6-8 */
}

.dark-style .navbar-default {
  background-color: #080808;
  background-image: none;
  border-bottom: 3px solid #ff7319;
}

@media (max-width: 767px) {
  #vertical .navbar-brand {
    width: 60px;
  }

  #vertical .ttmenu-full.open {
    max-width: 100%;
    width: 100% !important;
    position: relative;
  }

  #vertical .navbar-default,
  #vertical .navbar-default .nav,
  #vertical .navbar-default .collapse,
  #vertical .navbar-default .dropup,
  #vertical .navbar-default .dropdown {
    position: static;
    width: 100% !important;
    left: 0;
  }

  #vertical .navbar-collapse {
    padding: 0;
  }

  #vertical .navbar-default .navbar-nav li .vertical-menu {
    opacity: 1;
    position: static;
    left: 100% !important;
    right: 100% !important;
    top: auto;
    margin-top: 0 !important;
    max-width: 100%;
    padding: 0;
  }

  #vertical .navbar-default .navbar-nav li .vertical-dropdown-menu {
    opacity: 1;
    position: static;
    left: 100% !important;
    right: 100% !important;
    top: auto;
    margin-top: 0 !important;
    max-width: 100%;
    padding: 0;
  }

  #vertical .navbar-collapse {
    padding: 0 0 0 20px;
  }

  #vertical .dropdown-menu,
  #vertical .navbar-default .ttmenu-content {
    padding: 20px;
  }

  #vertical .nav-pills {
    max-width: 200px;
  }

  #vertical .dropme::after {
    content: "\f107";
    color: #fff;
    position: absolute;
    right: 20px;
    padding-left: 5px;
    font-family: "FontAwesome";
  }

  .nav-pills > li > a::after,
  .dropme-left::after {
    content: "\f107";
  }

  .ttmenu .nav-pills,
  #vertical .nav-pills {
    width: 100%;
    padding: 0;
    max-width: 100%;
    margin-bottom: 20px;
  }
}

.ttmenu .dropdown-submenu {
  position: relative;
}

.ttmenu .dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: 0;
  margin-left: 0;
  border-radius: 0;
}

.ttmenu .dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

.ttmenu .dropdown-submenu.pull-left {
  float: none;
}

.ttmenu .dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}

.ttmenu .open > .dropdown-menu {
  animation-name: slidenavAnimation;
  animation-duration: 0.5s;
  animation-iteration-count: 1;
  animation-timing-function: ease;
  animation-fill-mode: forwards;
  -webkit-animation-name: slidenavAnimation;
  -webkit-animation-duration: 0.5s;
  -webkit-animation-iteration-count: 1;
  -webkit-animation-timing-function: ease;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-name: slidenavAnimation;
  -moz-animation-duration: 0.5s;
  -moz-animation-iteration-count: 1;
  -moz-animation-timing-function: ease;
  -moz-animation-fill-mode: forwards;
}

@keyframes slidenavAnimation {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@-webkit-keyframes slidenavAnimation {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/***** NOVE VESTI *****/

.news-box {
  background-color: #f4f4f4;
  border: 1px solid #ccc;
  border-bottom: 3px solid #ccc;
  padding: 20px;
  margin-bottom: 25px;
}

.news-box a {
  color: #080808;
}

.news-box a:hover {
  color: #f27223;
}

h2.news-heading {
  font-family: "Roboto Condensed", sans-serif;
  font-size: 24px;
  margin-top: 0;
  line-height: 1;
}

.btn-primary-new {
  background-color: #ff970c;
  border-radius: 0;
  color: #fff;
  font-family: "Roboto Condensed", sans-serif;
}

.btn-primary-new:hover {
  background-color: #f27223;
  color: #fff;
}

.btn-news-more {
  float: right;
}

.single-news-text {
  margin-top: 25px;
}

.single-news-text p {
  font-size: 16px;
}

ul.news-arhive {
  border: 1px solid #ccc;
  padding-left: 0;
  border-bottom: 3px solid #ccc;
}

ul.news-arhive li {
  list-style-image: none !important;
  list-style-type: none;
  padding: 8px 10px;
}

ul.news-arhive li:nth-child(odd) {
  background: #f4f4f4;
}

ul.news-arhive li a {
  color: #080808;
  font-size: 20px;
  border-left: 3px solid #ff7319;
  padding-left: 10px;
}

ul.news-arhive li a:hover {
  color: #ff7319;
}

.form-control {
  border-radius: 0;
  height: 31px;
  border: 1px solid #ccc;
}

h1 {
  margin: 0 0 40px;
  padding: 20px 15px 15px;
  background: url("../heading-underline.png") no-repeat scroll center bottom;
  font-family: "Roboto Condensed", sans-serif;
  font-size: 28px;
}

.navbar-right .dropdown-menu {
  right: auto;
}

.navbar-default .dropdown-menu li a {
  font-size: 15px;
}

.navbar-default .dropdown-menu span {
  margin-right: 15px;
}

.navbar-default .navbar-nav > li > a .fa {
  font-size: 20px;
}

.kontakt-s {
  text-align: center;
  margin-bottom: 10px;
}

.modal-header {
  background: rgba(0, 0, 1, 0.075);
  border-bottom: 4px solid orange;
}

.modal-body h4 {
  border-radius: 6px;
  background-image: -webkit-linear-gradient(top, #f7971c 0, #f27223 100%);
  background-image: linear-gradient(to bottom, #f7971c 0, #f27223 100%);
  background-image: -webkit-gradient(
    linear,
    50% 0%,
    50% 100%,
    color-stop(0%, #f7971c),
    color-stop(100%, #f27223)
  );
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#fff27223', endColorstr='#fff7971c', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  background-repeat: repeat-x;
  color: #fff;
  padding: 8px;
  margin-bottom: 8px;
  font-size: 15px;
}

.modal-body h5 {
  border-radius: 6px;
  background-image: -webkit-linear-gradient(top, #f7971c 0, #f27223 100%);
  background-image: linear-gradient(to bottom, #f7971c 0, #f27223 100%);
  background-image: -webkit-gradient(
    linear,
    50% 0%,
    50% 100%,
    color-stop(0%, #f7971c),
    color-stop(100%, #f27223)
  );
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#fff27223', endColorstr='#fff7971c', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(enabled=false);
  background-repeat: repeat-x;
  color: #fff;
  padding: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

#footer .modal-dialog {
  width: 346px;
}

#footer .modal-body a {
  color: #000;
}

#footer .modal-title {
  margin: 0 !important;
}

#footer .modal-footer {
  border-top: 4px solid orange;
}

#footer .modal-footer a {
  color: #fff;
}

/* different headers */

.underline-header-three {
  font-size: 1.1em;
  border-bottom: 1px solid #ddd;
  padding-bottom: 4px;
}

.bigger-header-four {
  font-size: 15px;
  padding: 10px 0;
}

.box-header {
  margin-bottom: 10px;
  margin-right: 10px;
  font-size: 14px;
  padding: 6px;
  border-radius: 8px;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: all 0.3s linear 0s;
  -moz-transition: all 0.3s linear 0s;
  -ms-transition: all 0.3s linear 0s;
  -o-transition: all 0.3s linear 0s;
  transition: all 0.3s linear 0s;
}

.box-header:hover {
  -webkit-box-shadow: 0 1px 25px 0 rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 1px 25px 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 25px 0 rgba(0, 0, 0, 0.3);
}

.box-header a {
  display: block;
}

.box-header-first {
  background-image: url("https://www.akademijaoxford.com/images/patterns/black-thread-light.png");
}

.box-header-alt {
  background-image: url("https://www.akademijaoxford.com/images/patterns/cubes.png");
}

.modal-title h3 {
  /*  line-height: 29px;
  height: 58px;
  overflow: hidden;  */
  margin: 0 !important;
}

.box-header img {
  border: none;
  padding-right: 10px;
}

.desno-icons {
  padding: 0;
  max-width: 270px;
  margin: 0 auto;
  border-left: 1px solid #a2a194;
  border-right: 1px solid #a2a194;
  border-bottom: 1px solid #a2a194;
  background: #fff;
}

.desno-icons p {
  display: block;
  padding: 10px;
  text-align: center;
}

.desno-icons p img {
  margin-right: 10px;
}

.desno-icons-q {
  padding: 0;
  max-width: 270px;
  margin: 0 auto -16px auto;
  border: none;
}

.desno-icons-x {
  padding: 0;
  max-width: 270px;
  margin: 0 auto;
  border: none;
}

.desno-icons-xx {
  padding: 10px 0 5px 0;
  max-width: 200px;
  margin: 0 auto;
  border: none;
  /*text-align: center;*/
}

.img-center {
  float: none;
  margin: 0 auto;
}

h2.bar,
h2.bar-b {
  text-transform: uppercase;
}

.ispodlinija {
  border-bottom: 2px solid orange;
}

.ispodlinija:hover {
  border-bottom: 2px solid red;
}

/***** language switcher *****/

#style-switcher {
  background: #f4f4f4;
  right: -161px;
  position: fixed;
  top: 68px;
  width: 160px;
  z-index: 9999;
}

#style-switcher div {
  padding: 5px 9px;
  float: left;
}

.rock_themeoption {
  color: #000;
  margin-bottom: 15px;
}

ul.language-switcher-list {
  padding-left: 0;
}

ul.language-switcher-list li {
  list-style-type: none;
}

ul.language-switcher-list li:first-child {
  margin-bottom: 20px;
}

ul.language-switcher-list li a img {
  padding-right: 15px;
}

#style-switcher .bottom {
  background: none repeat scroll 0 0 #fff;
  color: #252525;
  padding: 0;
}

#style-switcher .bottom a.settings {
  background: #f4f4f4;
  display: block;
  height: 41px;
  position: absolute;
  left: -40px;
  top: 0;
  width: 40px;
  padding: 3px;
  font-family: sosa;
  line-height: 40px;
  color: #000;
}

#style-switcher .bottom a.settings i {
  font-size: 37px;
  color: #080808;
}

#style-switcher > div > select {
  width: 90%;
}

.slider_option > p > a > img {
  color: inherit;
  padding-right: 5px;
}

.slider_option > p > a:hover {
  color: inherit;
}

ul.pattern {
  list-style: none outside none;
  margin: 0 0 10px;
  overflow: hidden;
  padding: 0;
}

ul.pattern li {
  float: left;
  margin: 2px;
}

ul.pattern li p {
  cursor: pointer;
  display: block;
  border: 1px solid #696868;
  height: 22px;
  width: 22px;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  font-size: 0;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -ms-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
}

ul.pattern li:hover p {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
  -webkit-transition: all 0.2s;
  -moz-transition: all 0.2s;
  -ms-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
}

ul.pattern li #pattern {
  background: url(../images/patern/canvas-orange.jpg);
}

ul.pattern li #pattern1 {
  background: url(../images/patern/chalkboard.jpg);
}

ul.pattern li #pattern2 {
  background: url(../images/patern/ocean.jpg);
}

ul.pattern li #pattern3 {
  background: url(../images/patern/shattered-island.gif);
}

ul.pattern li #pattern4 {
  background: url(../images/patern/laser.gif);
}

ul.pattern li #style {
  background: #008363;
}

.news-text {
  padding-left: 20px;
}

ul.desno-pitanja {
  border: 1px solid #ff7319;
  padding-left: 0;
}

ul.desno-pitanja li {
  list-style-type: none;
  list-style-image: none;
  display: block;
  border-bottom: 1px solid #d4d4d4;
  padding: 10px;
}

ul.desno-pitanja li:last-child {
  border-bottom: none;
}

ul.desno-pitanja li a {
  font-family: "Roboto Condensed", sans-serif;
  font-size: 14px;
  color: #080808;
}

ul.desno-pitanja li a:hover {
  color: #ff7319;
}

.breadcrumb {
  margin-bottom: 20px;
  border-radius: 0;
}

.breadcrumb .fa {
  font-size: 18px;
}

.breadcrumb {
  background: rgba(245, 245, 245, 1);
  border: 0 solid rgba(245, 245, 245, 1);
  border-radius: 4px;
  display: block;
}
.breadcrumb li {
  font-size: 14px;
}

.breadcrumb a {
  color: rgb(255, 151, 12);
}
.breadcrumb a:hover {
  color: rgb(255, 115, 25);
}
.breadcrumb > .active {
  color: rgba(153, 153, 153, 1);
}

.breadcrumb > li + li::before {
  color: rgba(204, 204, 204, 1);
  content: "\276F\00a0";
}

p.kratak-opis {
  font-size: 22px;
  font-family: "Roboto Condensed";
}

.price {
  float: right;
}

.price p {
  font-family: "Roboto Condensed";
}

.price span {
  color: #ff7319;
  font-weight: bold;
  font-size: 18px;
  font-family: "Roboto Condensed";
}

.nav > li > a {
  padding: 0 10px;
}

.scrtabs-tab-scroll-arrow {
  width: 19px !important;
}

@media screen and (max-width: 800px) {
  body {
    padding-top: 65px;
  }

  .navbar-brand {
    padding-left: 7px;
  }

  .news-text {
    padding-left: 0;
    padding-top: 10px;
  }
}

@media (min-width: 992px) {
  .navbar-default .navbar-nav > li > a {
    font-size: 11px;
  }
}

@media (min-width: 1200px) {
  .navbar-default .navbar-nav > li > a {
    font-size: 13px;
  }
}

/* Novi Meni */

.navbar.navbar-fixed-top.navbar-inverse {
  border-bottom: 3px solid #ff7319;
}

.navbar-inverse {
  background-color: #080808;
}

.navbar-inverse .navbar-nav > li > a {
  font-family: "Roboto Condensed", sans-serif;
  font-size: 13px;
  color: #fff;
  padding: 22px 10px 21px;
}

.navbar-inverse .navbar-nav > li > a .fa {
  font-size: 20px;
}

.navbar-brand {
  height: 65px;
  line-height: 65px;
  padding: 7px 0;
}

.navbar-collapse {
  border-top: 0;
}

.navbar-default .dropdown-menu li a {
  border-bottom: 0;
}

.ttmenu .navbar-default .ttmenu-content {
  padding: 0;
}

.ttmenu-content .box li a {
  padding: 13px 10px !important;
}

.navbar-default .dropdown-menu li a {
  padding: 13px 10px;
}

@media screen and (max-width: 768px) {
  .side-collapse-container {
    width: 100%;
    position: relative;
    right: 0;
    transition: right 0.2s;
  }

  .side-collapse-container.out {
    right: 300px;
  }

  .side-collapse {
    top: 68px;
    bottom: 0;
    right: 0;
    width: 100%;
    position: fixed;
    overflow: scroll;
    transition: width 0.2s;
  }

  .side-collapse.in {
    width: 0;
  }

  ul.navbar-right > li {
    border-bottom: 1px solid #333;
  }

  .navbar-nav {
    margin-top: 0;
  }

  ul.dropdown-menu {
    border-left: 3px solid #ff7319 !important;
  }

  .ttmenu-content .box li a {
    left: 0;
  }
}

ul.list-unstyled li {
  list-style-image: none;
}

.portfolio {
  margin: 48px 0;
}

ul.portfolio-sorting {
  font-size: 16px;
  padding-left: 0;
  margin-bottom: 0;
  background: #fff;
}

ul.portfolio-sorting li {
  list-style-image: none;
  list-style-type: none;
  padding: 0;
  border-bottom: 1px solid #d4d4d4;
}

ul.portfolio-sorting li:last-child {
  border-bottom: 0;
}

ul.portfolio-sorting li a {
  color: #080808;
  text-decoration: none;
  padding: 15px;
  transition: all 0.15s ease;
  border-left: 0 solid #ff7319;
  display: block;
}

.portfolio-sorting li a:hover,
.portfolio-sorting li a.active {
  color: #ff7319;
  border-left: 6px solid #ff7319;
}

ul.portfolio-items {
  padding-left: 0;
}

ul.portfolio-items li {
  padding-bottom: 0;
  list-style-image: none;
  list-style-type: none;
}

.portfolio-item {
  padding-left: 10px;
  padding-right: 10px;
  margin-bottom: 20px;
}

figure.portfolio-item > a {
  font-family: "Roboto Condensed", sans-serif !important;
  font-size: 16px;
  margin: 8px 0;
  color: #ff7319;
  display: block;
}

figure.portfolio-item > p {
  text-align: left;
  margin-bottom: 0;
}

.categories-box {
  border: 1px solid #ff7319;
  font-family: "Roboto Condensed", sans-serif !important;
}

.categories-box h2 {
  margin: 0;
  background: #ff7319;
  color: #fff;
  padding: 15px;
}

figure {
  margin-bottom: 56px !important;
}

/* vimeo responsive */

.embed-container {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
  max-width: 100%;
}
.embed-container iframe,
.embed-container object,
.embed-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

a,
a:visited,
a:focus,
a:active,
a:hover {
  outline: 0 none !important;
}
button,
button:visited,
button:focus,
button:active,
button:hover {
  outline: 0 none !important;
}
input:focus,
textarea:focus,
button:focus {
  outline: none !important;
}

/**********************/

/**********************/

/** search field ***/
#srch {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);

  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;

  -webkit-transform: translate(0px, -100%) scale(0, 0);
  -moz-transform: translate(0px, -100%) scale(0, 0);
  -o-transform: translate(0px, -100%) scale(0, 0);
  -ms-transform: translate(0px, -100%) scale(0, 0);
  transform: translate(0px, -100%) scale(0, 0);

  opacity: 0;
}

#srch.open {
  -webkit-transform: translate(0px, 0px) scale(1, 1);
  -moz-transform: translate(0px, 0px) scale(1, 1);
  -o-transform: translate(0px, 0px) scale(1, 1);
  -ms-transform: translate(0px, 0px) scale(1, 1);
  transform: translate(0px, 0px) scale(1, 1);
  opacity: 1;
  z-index: 9999;
}

#srch input[type="srch"] {
  position: absolute;
  top: 50%;
  width: 100%;
  color: rgb(255, 255, 255);
  background: rgba(0, 0, 0, 0);
  font-size: 60px;
  font-weight: 300;
  text-align: center;
  border: 0px;
  margin: 0px auto;
  margin-top: -51px;
  padding-left: 30px;
  padding-right: 30px;
  outline: none;
}
#srch .btn {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: 61px;
  margin-left: -45px;
}
#srch .close {
  position: fixed;
  top: 15px;
  right: 15px;
  color: #fff;
  background-color: #ff970c;
  border-color: #ff970c;
  opacity: 1;
  padding: 10px 17px;
  font-size: 27px;
}
#srch input:focus {
  border: 0;
  outline: none;
}

/*** placement/profile test website inclusion ***/

.grid {
  width: 100%;
  margin: 0 auto;
  background-color: #ff970c;
  padding: 5px;
}

.grid-title {
  background-color: #fff;
}

.grid-title-link {
  margin-bottom: 10px;
}

.grid-title h2 {
  display: inline-block;
  padding: 5px 15px;
  font-weight: 700;
  font-size: 25px;
  text-transform: uppercase;
  background-color: #ff970c;
  color: #fff;
}

.grid-title-link a {
  /*display: inline-block;*/
  padding: 5px 15px;
  font-weight: 700;
  font-size: 20px;
  text-transform: uppercase;
  background-color: hotpink;
  color: #fff;
  -webkit-transition-property: background-color;
  -webkit-transition-duration: 0.2s;
  -webkit-transition-delay: 0.1s;
  transition-property: background-color;
  transition-duration: 0.2s;
  transition-delay: 0.1s;
}

.grid-title-link a:hover,
.grid-title-link a:active {
  background-color: #ff970c;
}

.link_box {
  display: block;
  padding: 5px;
  width: 100%;
  /*height: 50px;*/
  overflow: hidden;
  background-color: #ff7319;
  text-align: center;
  position: relative;
  cursor: pointer;
}

.overlay {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.rotate_into {
  padding: 5px;
}

.rotate_into span {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
  line-height: 0;
}

.rotate_into .overlay {
  background-color: #ff970c;
  /*line-height: 50px;*/
  color: #fff;
  transform-origin: 0 0;
  transform: rotate(90deg);
  -webkit-transition: transform 0.5s ease-in-out;
  -o-transition: transform 0.5s ease-in-out;
  transition: transform 0.5s ease-in-out;
}

.rotate_into .link_box:hover .overlay {
  transform: rotate(0deg);
}

/* calc */

.header-calc {
  color: #616161;
  text-align: center;
  font-size: 18px;
  font-style: italic;
  text-transform: uppercase;
  text-shadow: #e0e0e0 1px 1px 0;
  padding-bottom: 5px;
  background: linear-gradient(
      to left,
      rgba(255, 165, 0, 1) 0%,
      rgba(255, 150, 0, 1) 12%,
      rgba(255, 115, 25, 1) 47%,
      rgba(255, 255, 255, 1) 100%
    )
    left bottom #fff no-repeat;
  background-size: 100% 3px;
}

.disclaimer {
  color: gray;
  font-size: 12px;
  text-transform: uppercase;
  padding: 10px 0 2px 0;
}

.disclaimer a {
  color: gray;
  border-bottom: 3px solid gainsboro;
  font-weight: bold;
  -webkit-transition: border-color 0.5s ease-out;
  -moz-transition: border-color 0.5s ease-out;
  -o-transition: border-color 0.5s ease-out;
  transition: border-color 0.5s ease-out;
}

.disclaimer a:visited,
.disclaimer a:focus,
.disclaimer a:active,
.disclaimer a:hover {
  border-color: #ff7319;
}

/* langPrice */

.calcSelectedOffice {
  padding: 25px;
  font-size: 1.2em;
}

#langOfficeTableId {
  font-size: 13px;
}

#langOfficeTableId td {
  text-align: center;
}

.langOfficeTH {
  width: 100%;
}

.langOfficeTH td {
  background-color: #000;
  color: #fff;
  border: 1px solid #fff;
  text-align: center;
}

.scrollbar {
  height: 250px;
  overflow-y: scroll;
  overflow-x: hidden;
  margin-top: 30px;
  margin-bottom: 15px;
}

#tableContainerId::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

#tableContainerId::-webkit-scrollbar {
  width: 10px;
  background-color: #f5f5f5;
}

#tableContainerId::-webkit-scrollbar-thumb {
  background-color: #f90;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

.table__select {
  background-color: orange;
}

#napomena {
  margin: 0 15px 0 10px;
  text-align: justify;
}

/* online kursevi */

.categories-box {
  margin: 25px 0 20px 0;
}

.categories-box h2 {
  font-size: 26px;
  text-transform: uppercase;
}

/*butstrep over-rajd*/
.content .boardz ul li {
  list-style-image: none;
}

.boardz {
  display: flex;
  justify-content: space-between;
  flex: auto;
}

.boardz ul {
  list-style-type: none;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px 0px 4px 0px;
  flex: 1;
}

.boardz ul li {
  margin: 8px 4px 8px 4px;
  padding: 12px;
  flex: auto;
  background-color: #ffffff;
  /*border: 1px solid #737373;*/
  -webkit-border-image: linear-gradient(120deg, #ccc, #eee) 1;
  -o-border-image: linear-gradient(120deg, #ccc, #eee) 1;
  border-image: linear-gradient(120deg, #ccc, #eee) 1;
  border-width: 2px;
  border-style: solid;
  text-align: center;
}

.boardz ul h3 {
  margin: 0 0 10px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid gray;
  font-size: 24px;
}

@media screen and (max-width: 800px) {
  .boardz {
    flex-direction: column;
  }
  .boardz ul li:last-child {
    margin-bottom: 0;
  }
  .boardz ul {
    margin: 0;
    padding: 0;
  }
}

.fitted ul {
  display: flex;
}

.fitted ul li {
  margin: 4px 4px 4px 4px !important;
}

.boardz img {
  max-width: 100%;
  height: auto;
}

.boardz a {
  /*font-family: 'Montserrat', sans-serif;*/
  display: inline-block;
  text-transform: uppercase;
  color: #ff7319;
  text-decoration: none;
  border: 2px solid;
  background: transparent;
  padding: 2px 40px;
  font-size: 22px;
  font-weight: 700;
  -webkit-transition: 0.2s all;
  transition: 0.2s all;
  margin-top: 4px;
}

.boardz a:hover {
  color: #ff970c;
}

.cat__i {
  padding-bottom: 10px;
}

sup {
  font-family: "Arial";
  text-transform: uppercase;
  font-size: 11px;
  color: green;
  white-space: nowrap;
}

.ex__border {
  margin: 0 4px 12px 4px !important;
  padding: 12px;
  -webkit-border-image: linear-gradient(120deg, #ccc, #eee) 1;
  -o-border-image: linear-gradient(120deg, #ccc, #eee) 1;
  border-image: linear-gradient(120deg, #ccc, #eee) 1;
  border-width: 2px;
  border-style: solid;
}

.c__link {
  font-size: 20px !important;
  line-height: 1.2em;
  /*color: #fd32be !important;*/
}

.flunder {
  display: inline-block;
  color: #ff7319;
  text-decoration: none;
}

.flunder::after {
  content: "";
  display: block;
  width: 0;
  height: 3px;
  background: #aaa;
  background: -webkit-linear-gradient(
    110deg,
    #e1f549,
    #29d0be,
    #6cb8ea,
    #ff5959
  );
  background: -o-linear-gradient(110deg, #e1f549, #29d0be, #6cb8ea, #ff5959);
  background: -moz-linear-gradient(110deg, #e1f549, #29d0be, #6cb8ea, #ff5959);
  background: linear-gradient(110deg, #e1f549, #29d0be, #6cb8ea, #ff5959);
  transition: width 0.3s;
}

.flunder:hover::after {
  width: 100%;
  transition: width 0.3s;
}

.btn__cont {
  margin: 25px 0;
}

.btn__ex {
  cursor: pointer;
  border-radius: 0px;
  padding: 12px 18px;
  font-family: "Montserrat", sans-serif !important;
  font-weight: 700;
  font-size: 12px;
  line-height: 19px;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 3px;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

.btn__ex:hover {
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -ms-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

.btn-dark {
  border: solid 3px #ccc;
  background: #fff;
  color: #aaa;
}

.btn-dark:hover,
.btn-dark:focus,
.btn-dark:active {
  border: solid 3px #ff7319;
  color: #ff7319;
}

.line--gradient {
  position: relative;
  margin-bottom: 2px;
  margin-top: 22px;
}

.line--gradient:before,
.line--gradient:after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
}

.line--gradient:after {
  right: 0;
  height: 2px;
  background-color: #ff970c;
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuMCIgeTE9IjAuNSIgeDI9IjEuMCIgeTI9IjAuNSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwODNmNiIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzVkY2RmYSIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==");
  background-size: 100%;
  background-image: -webkit-gradient(
    linear,
    0% 50%,
    100% 50%,
    color-stop(0%, #ff970c),
    color-stop(100%, #fff)
  );
  background-image: -moz-linear-gradient(left, #ff970c, #fff);
  background-image: -webkit-linear-gradient(left, #ff970c, #fff);
  background-image: linear-gradient(to right, #ff970c, #fff);
}

.line--gradient h2 {
  color: #666;
  font-family: "Montserrat", sans-serif;
  font-size: 24px;
  font-weight: 300;
  line-height: 32px;
  margin: 0 0 32px;
  text-transform: uppercase;
}

.category__slab {
  background: #fff;
  -webkit-transition: background-color 1s ease-out;
  -moz-transition: background-color 1s ease-out;
  -o-transition: background-color 1s ease-out;
  transition: background-color 1s ease-out;
  border-image: linear-gradient(120deg, #ff970c, #fafafa) 1;
  border-width: 2px;
  border-style: solid;
  padding: 10px;
  margin: 4px;
}

.category__slab h3 {
  color: #e8640c;
  font-family: "Montserrat", sans-serif;
  font-size: 1.3em;
  font-weight: normal;
  text-transform: uppercase;
}

.category__slab h3 a,
.category__slab h3 a:visited,
.category__slab h3 a:active {
  color: #e8640c;
  border-bottom: 2px solid rgba(192, 192, 192, 0);
  -webkit-transition: border-bottom 0.5s ease;
  -moz-transition: border-bottom 0.5s ease;
  -ms-transition: border-bottom 0.5s ease;
  -o-transition: border-bottom 0.5s ease;
  transition: border-bottom 0.5s ease;
}

.category__slab h3 a:hover {
  border-bottom: 2px solid rgba(192, 192, 192, 1);
}

.category__slab p {
  font-size: 0.9em;
  line-height: 1em;
  color: #7a7a7a;
}

.category__pic {
  position: relative;
}

.category__pic img {
  width: 100%;
  max-width: 100%;
  height: auto;
}

.tint {
  /*cursor: pointer;*/
  box-shadow: rgba(0, 0, 0, 0.2) 1px 3px 3px;
}

.tint:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 113, 57, 0.1);
  transition: all 0.3s linear;
}

.tint:hover:before {
  background: none;
}

.course__slab {
  background: white;
  border-image: linear-gradient(120deg, #ff970c, #fafafa) 1;
  border-width: 2px;
  border-style: solid;
  padding: 10px;
  margin: 4px;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.course__slab p {
  font-size: 0.9em;
  line-height: 1em;
  padding-top: 10px;
  color: #7a7a7a;
}

.course__pic {
  position: relative;
}

.course__pic img {
  width: 100%;
  max-width: 100%;
  height: auto;
}

.course__pic h3 {
  position: absolute;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: #999;
  font-weight: 300;
  font-size: 1em;
  line-height: 1.1em;
  text-transform: uppercase;
  bottom: 0;
  left: 0;
  padding: 4px 8px;
  margin: 0;
}

.course__pic h3 a {
  color: #fff;
  display: block;
}

@media (max-width: 1024px) {
  .category__slab,
  .course__slab {
    margin: 4px 4px 10px;
  }
}

.category__slab:hover,
.course__slab:hover {
  background-color: snow;
}

.transbox {
  margin: 30px 0 2px 0;
  border-bottom: 3px solid darkorange;
}

.transbox p {
  text-transform: uppercase;
  text-align: center;
  font-style: italic;
  font-size: 0.9em;
  line-height: 1em;
  letter-spacing: 0.2em;
  background-color: black;
  color: darkorange;
  padding: 16px;
}

.korisne-informacije a {
  display: block;
}
.korisne-informacije img {
  padding: 6px 6px 0px 6px;
  margin-bottom: 6px;
}
.korisne-informacije h2 {
  font-size: 24px;
  margin-bottom: 25px;
}
.popover.top {
  margin-top: -5px;
  border-radius: 0;
  padding: 0;
}
.popover.top > .arrow:after {
  border-top-color: #424045;
}
.popover-title {
  background-color: #424045;
  color: #fff;
  font-weight: 700;
  border-radius: 0;
  border: 0;
  padding: 4px 8px;
}
.popover-content {
  padding: 0;
  font-size: 13px;
}
.popover-content > div {
  padding: 8px;
}
.popover-content span {
  font-weight: 700;
}
.info-single-box {
  display: flex;
  flex: 0 0 100%;
  height: 50px;
  background: #f4f4f4;
  align-items: center;
  margin-bottom: 30px;
}
.info-icon {
  display: flex;
  flex: 0 0 50px;
  align-items: center;
  height: 50px;
}
.orange {
  background: #ff970c;
}
.viber {
  background: #665cac;
}
.whatsapp {
  background: #4fce5d;
}
.info-icon img {
  max-width: 65%;
  margin: 0 auto;
}
.info-text p {
  margin-bottom: 0;
  font-size: 15px !important;
  padding-left: 10px !important;
  padding-right: 5px !important;
  background-color: transparent !important;
}
.pr-15 {
  padding-right: 15px;
}
.pl-15 {
  padding-left: 15px;
}
.mb-50 {
  margin-bottom: 50px;
}
@media (max-width: 991px) {
  .info-text p {
    font-size: 14px;
  }
  .pr-15 {
    padding-right: 0;
  }
  .pl-15 {
    padding-left: 0;
  }
}

/* cena box */

#cena__box {
  padding: 10px;
  margin: 10px 10px 20px 10px;
  font-size: 2.5rem;
  color: #789;
  text-align: center;
  text-transform: uppercase;
}

.cena__gradient {
  --borderWidth: 3px;
  background: #fff;
  position: relative;
  border-radius: 0;
}

.cena__gradient:after {
  content: "";
  position: absolute;
  top: calc(-1 * var(--borderWidth));
  left: calc(-1 * var(--borderWidth));
  height: calc(100% + var(--borderWidth) * 2);
  width: calc(100% + var(--borderWidth) * 2);
  background: linear-gradient(
    60deg,
    #f79533,
    #f37055,
    #ef4e7b,
    #a166ab,
    #5073b8,
    #1098ad,
    #07b39b,
    #6fba82
  );
  border-radius: 0;
  z-index: -1;
  animation: animatedgradient 3s ease alternate infinite;
  background-size: 300% 300%;
}

@keyframes animatedgradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 2021 course redesign */

.cef__symb {
  text-align: center;
}

.cef__symb h3 {
  font-size: 4em;
  font-weight: 700;
}

.group__symb img {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 50%;
  max-width: 344px;
  height: auto;
}

.p-arrow {
  border: solid #ff7319;
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 3px;
}

.p-right {
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
}

.p-nowrap {
  white-space: nowrap;
}

.divider-trans {
  position: relative;
  height: 1px;
}

.divider-trans:before {
  content: "";
  position: absolute;
  top: 0;
  left: 5%;
  right: 5%;
  width: 90%;
  height: 1px;
  background-image: linear-gradient(
    to right,
    transparent,
    rgb(195, 195, 195),
    transparent
  );
}

.arrow-down {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #fff;
}

.desno__img {
  border-style: solid;
  border-width: 3px;
  border-image: conic-gradient(
      grey,
      white,
      lightgrey,
      darkgrey,
      orange,
      aliceblue,
      grey
    )
    1;
}

.main__img {
  border-style: solid;
  border-width: 3px;
  border-image: conic-gradient(
      grey,
      aliceblue,
      orange,
      darkgrey,
      lightgrey,
      white,
      grey
    )
    1;
}

.course__img {
  border-style: solid;
  border-width: 2px;
  border-image: linear-gradient(-120deg, #afafaf, #fafafa) 1;
  max-width: 500px;
}

.button__dd {
  width: 100%;
  text-decoration: none;
  text-transform: uppercase;
  font-family: "Fjalla One", sans-serif;
  /*font-weight: normal;*/
  font-size: 18px;
  display: inline-block;
  position: relative;
  text-align: center;
  color: #333;
  background-color: #fff;
  border: 3px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(to left, #666, #999);
  border-radius: 0;
  /*line-height: 1.5em;
  padding-left: 2em;
  padding-right: 2em;*/
  padding: 1em;
  /*box-shadow: 0 0 0 0 transparent;*/
  -webkit-transition: all 0.2s ease-in;
  -moz-transition: all 0.2s ease-in;
  transition: all 0.2s ease-in;
}

.button__dd:hover {
  /*color: #333;*/
  background-color: #f0f0f0;
  -webkit-transition: all 0.2s ease-out;
  -moz-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.button__dd:active,
.button__dd:focus {
  color: #333;
}

.a-list-apart {
  padding: 15px 0 0 0;
}

ul.a-list-apart li {
  font-size: 13px;
  list-style: none;
}

ul.a-list-apart li a:before {
  content: "\A";
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ff970c;
  display: inline-block;
  margin: 0 5px;
}

ul.centrirano li {
  padding-bottom: 12px;
}

.btn-bot {
  background: transparent;
  border: 2px solid transparent;
  box-sizing: border-box;
  cursor: pointer;
  font-size: 1em;
  font-weight: 700;
  line-height: 1;
  text-transform: uppercase;
  margin: 18px;
  padding: 15px 25px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  outline: none;
  position: relative;
  top: 0;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
}

.btn-bot-orange {
  background: #e77e22;
  color: #fff;
}

.btn-bot-orange:hover {
  background: #ff7319;
  color: #fff;
}

.btn-bot-orange:active,
.btn-bot-orange:focus {
  color: #fff;
}

.wobble-horizontal {
  display: inline-block;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  transform: translateZ(0);
  box-shadow: 0 0 1px rgba(0, 0, 0, 0);
}

.wobble-horizontal:hover {
  animation-name: wobble-horizontal;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

@keyframes wobble-horizontal {
  16.65% {
    transform: translateX(8px);
  }
  33.3% {
    transform: translateX(-6px);
  }
  49.95% {
    transform: translateX(4px);
  }
  66.6% {
    transform: translateX(-2px);
  }
  83.25% {
    transform: translateX(1px);
  }
  100% {
    transform: translateX(0);
  }
}

h2.spec__ttl a {
  border-bottom: 2px solid #eee;
  transition: 0.3s;
}

h2.spec__ttl a:hover {
  border-bottom-color: #ff7319;
}

#cene {
  width: 100%;
}

#cene table {
  border-collapse: collapse;
  font-family: "Roboto Condensed", sans-serif;
  font-size: 14px;
  font-weight: normal;
}

#cene table,
#cene th,
#cene td {
  border: 1px solid #333;
}

#cene th {
  text-transform: uppercase;
  vertical-align: middle !important;
  text-align: center;
  overflow: hidden;
  height: 57px;
  background: #333;
  /*background: -webkit-linear-gradient(to right, #666, #333);
  background: linear-gradient(to right, #666, #333);*/
  color: #fff;
}

#cene td {
  text-align: center;
  vertical-align: middle !important;
  overflow: hidden;
  height: 57px;
  min-width: 100px;
  background-color: #fff;
}

#cene .left__table td {
  word-break: break-all;
}

@media only screen and (max-width: 1200px) {
  #cene .left__table td {
    font-size: 12px;
    line-height: 11px;
  }
}

#cene .right__table td {
  white-space: nowrap;
}

.scroll__me {
  overflow-x: scroll;
}

.scroll__me::-webkit-scrollbar {
  width: 12px;
}

.scroll__me::-webkit-scrollbar-track {
  background-color: #333;
}

.scroll__me::-webkit-scrollbar-thumb {
  background: #ff7319; /* fallback for old browsers */
  background: -webkit-linear-gradient(
    to right,
    #ff970c,
    #ff7319
  ); /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(
    to right,
    #ff970c,
    #ff7319
  ); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

/* AO language selector */

#country-container {
  width: 200px;
  padding: 10px;
  margin: 0 auto 15px;
  text-align: center;
  background: #f8f8f8;
  border-radius: 0 0 10px 10px;
  list-style: none;
}

ul#country-container li {
  display: inline;
}

#country-container a {
  opacity: 1;
  transition: 1s ease;
}

#country-container a:hover {
  opacity: 0.7;
  transition: 1s ease;
}

/* Call to Action */

.cta-box {
  margin: 25px 0;
}

.cta-box-color-i {
  border-radius: 20px;
  background: rgb(0, 0, 0);
  background: linear-gradient(
    -45deg,
    rgba(0, 0, 0, 1) 0%,
    rgba(51, 204, 255, 1) 100%
  );
}

.cta-box-color-ii {
  border-radius: 20px;
  background: rgb(0, 0, 0);
  background: linear-gradient(
    -45deg,
    rgba(0, 0, 0, 1) 0%,
    rgba(161, 44, 47, 1) 100%
  );
}

.cta-box img {
  padding: 15px;
}

.cta-box h2 {
  color: #fff;
  font-size: 2.5rem;
  line-height: 25px;
  text-transform: uppercase;
  padding: 0 25px;
}

.cta-box p {
  color: #fff;
  padding: 0 25px;
}

@media (min-width: 768px) {
  .cta-box h2 {
    padding: 10px 25px 10px 10px;
  }
  .cta-box p {
    padding: 0 25px 10px 10px;
  }
}

.cta-box a {
  margin-bottom: 25px;
}

.btn-cta,
.btn-cta:focus,
.btn-cta:active {
  background-color: #000;
  color: #fff;
}

.btn-cta:hover {
  background-color: #fff;
  color: #000;
}