// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],
  build: {
    transpile: ['bootstrap']
  },
  app: {
    head: {
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'robots', content: 'index, follow' },
        { name: 'revisit-after', content: '3 days' },
        { name: 'rating', content: 'General' },
        { name: 'distribution', content: 'Global' },
        { property: 'fb:page_id', content: '291695574213604' },
        { property: 'og:site_name', content: 'Akademija Oxford' }
      ],
      link: [
        {
          rel: 'preconnect',
          href: 'https://fonts.googleapis.com'
        },
        {
          rel: 'preconnect',
          href: 'https://fonts.gstatic.com',
          crossorigin: ''
        },
        {
          rel: 'stylesheet',
          href: 'https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap'
        },
        {
          rel: 'stylesheet',
          href: 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css'
        },
        { rel: 'apple-touch-icon', sizes: '180x180', href: 'https://www.akademijaoxford.com/favicons/apple-touch-icon.png' },
        { rel: 'icon', type: 'image/png', href: 'https://www.akademijaoxford.com/favicons/favicon-32x32.png', sizes: '32x32' },
        { rel: 'icon', type: 'image/png', href: 'https://www.akademijaoxford.com/favicons/favicon-16x16.png', sizes: '16x16' },
        { rel: 'manifest', href: 'https://www.akademijaoxford.com/favicons/manifest.json' },
        { rel: 'mask-icon', href: 'https://www.akademijaoxford.com/favicons/safari-pinned-tab.svg', color: '#5bbad5' },
        { rel: 'shortcut icon', href: 'https://www.akademijaoxford.com/favicons/favicon.ico' }
      ],
      script: [
        // Dodajem CDN Bootstrap kao fallback
        {
          src: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
          defer: true
        }
      ]
    }
  },
  ssr: false
})
