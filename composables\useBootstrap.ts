import * as bootstrap from 'bootstrap'

export const useBootstrap = () => {
  const initializeDropdowns = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem dropdown komponente...')

        // Uni<PERSON>ti postojeće dropdown instance da <PERSON><PERSON><PERSON><PERSON> duplikate
        const existingDropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Pronašao ${existingDropdowns.length} postojećih dropdown elemenata`)
        existingDropdowns.forEach(element => {
          const existingInstance = bootstrap.Dropdown.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove dropdown instance
        const dropdownElementList = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        console.log(`Kreiram ${dropdownElementList.length} novih dropdown instanci`)

        const dropdownList = [...dropdownElementList].map(dropdownToggleEl => {
          console.log('Kreiram dropdown za element:', dropdownToggleEl)

          // Dodaj eksplicitni event listener za hover i click
          dropdownToggleEl.addEventListener('mouseenter', function() {
            // Hover efekat za desktop
            if (window.innerWidth >= 992) {
              const dropdown = bootstrap.Dropdown.getInstance(this) || new bootstrap.Dropdown(this)
              dropdown.show()
            }
          })

          dropdownToggleEl.addEventListener('mouseleave', function() {
            // Sakrij dropdown kada miš napusti element (samo za desktop)
            if (window.innerWidth >= 992) {
              setTimeout(() => {
                const dropdown = bootstrap.Dropdown.getInstance(this)
                if (dropdown) {
                  dropdown.hide()
                }
              }, 100)
            }
          })

          return new bootstrap.Dropdown(dropdownToggleEl, {
            // Opcije za dropdown
            autoClose: true,
            boundary: 'clippingParents'
          })
        })

        console.log(`Inicijalizovano ${dropdownList.length} dropdown komponenti`)
        return dropdownList
      })
    }
  }

  const initializeCollapses = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem collapse komponente...')

        // Uništi postojeće collapse instance
        const existingCollapses = document.querySelectorAll('[data-bs-toggle="collapse"]')
        console.log(`Pronašao ${existingCollapses.length} postojećih collapse elemenata`)
        existingCollapses.forEach(element => {
          const existingInstance = bootstrap.Collapse.getInstance(element)
          if (existingInstance) {
            existingInstance.dispose()
          }
        })

        // Kreiraj nove collapse instance
        const collapseElementList = document.querySelectorAll('[data-bs-toggle="collapse"]')
        const collapseList = [...collapseElementList].map(collapseToggleEl => {
          return new bootstrap.Collapse(collapseToggleEl, {
            toggle: false
          })
        })

        console.log(`Inicijalizovano ${collapseList.length} collapse komponenti`)
        return collapseList
      })
    }
  }

  const initializeModals = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem modal komponente...')

        // Uništi postojeće modal instance
        const existingModals = document.querySelectorAll('[data-bs-toggle="modal"]')
        console.log(`Pronašao ${existingModals.length} postojećih modal elemenata`)
        existingModals.forEach(element => {
          const targetSelector = element.getAttribute('data-bs-target')
          if (targetSelector) {
            const targetElement = document.querySelector(targetSelector)
            if (targetElement) {
              const existingInstance = bootstrap.Modal.getInstance(targetElement)
              if (existingInstance) {
                existingInstance.dispose()
              }
            }
          }
        })

        // Kreiraj nove modal instance
        const modalElementList = document.querySelectorAll('.modal')
        const modalList = [...modalElementList].map(modalEl => {
          return new bootstrap.Modal(modalEl)
        })

        console.log(`Inicijalizovano ${modalList.length} modal komponenti`)
        return modalList
      })
    }
  }

  const initializeSideCollapse = () => {
    if (process.client) {
      nextTick(() => {
        console.log('Inicijalizujem side-collapse funkcionalnost...')

        // Side-collapse funkcionalnost kao u ao.css
        const sideslider = document.querySelector('[data-toggle=collapse-side]')
        if (sideslider) {
          const sel = sideslider.getAttribute('data-target')
          const sel2 = sideslider.getAttribute('data-target-2')

          if (!sel || !sel2) {
            console.log('data-target ili data-target-2 atributi nisu pronađeni')
            return
          }

          // Ukloni postojeće event listenere da izbegneš duplikate
          const existingHandler = (sideslider as any)._sideCollapseHandler
          if (existingHandler) {
            sideslider.removeEventListener('click', existingHandler)
          }

          // Kreiraj novi handler
          const clickHandler = function() {
            console.log('Side-collapse dugme kliknuto')
            const target = document.querySelector(sel)
            const target2 = document.querySelector(sel2)

            console.log('Target elements:', { target, target2, sel, sel2 })

            if (target && target2) {
              if (target.classList.contains('in')) {
                target.classList.remove('in')
                target2.classList.add('out')
                console.log('Zatvaranje menija')
              } else {
                target.classList.add('in')
                target2.classList.remove('out')
                console.log('Otvaranje menija')
              }
            } else {
              console.log('Target elementi nisu pronađeni')
            }
          }

          // Dodeli handler i sačuvaj referencu
          sideslider.addEventListener('click', clickHandler)
          ;(sideslider as any)._sideCollapseHandler = clickHandler

          console.log('Side-collapse inicijalizovan')
        } else {
          console.log('Side-collapse dugme nije pronađeno')
        }
      })
    }
  }

  const initializeAllComponents = () => {
    console.log('Inicijalizujem sve Bootstrap komponente...')
    initializeDropdowns()
    initializeCollapses()
    initializeModals()
    initializeSideCollapse()
  }

  const getDropdownInstance = (element: Element) => {
    return bootstrap.Dropdown.getInstance(element)
  }

  const getCollapseInstance = (element: Element) => {
    return bootstrap.Collapse.getInstance(element)
  }

  const getModalInstance = (element: Element) => {
    return bootstrap.Modal.getInstance(element)
  }

  return {
    bootstrap,
    initializeDropdowns,
    initializeCollapses,
    initializeModals,
    initializeAllComponents,
    getDropdownInstance,
    getCollapseInstance,
    getModalInstance
  }
}
